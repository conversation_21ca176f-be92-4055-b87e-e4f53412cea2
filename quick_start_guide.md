# 🎓 University Email Filter - Quick Start Guide

## 🚀 Fastest Way to Get Started

### Option 1: Double-Click and Go (Windows)
1. **Double-click** `run_university_filter.bat`
2. **Enter** your CSV files directory path
3. **Press Enter** for default email column name ("<PERSON><PERSON>") or type your column name
4. **Press Enter** for auto output directory or specify custom path
5. **Done!** Your filtered files will be saved with "university_" prefix

### Option 2: Command Line (All Platforms)
```bash
# Basic usage - processes all CSV files in the directory
python university_email_filter.py "C:\path\to\your\csv\files"

# With custom email column name
python university_email_filter.py "C:\path\to\csv\files" --email-column "Contact_Email"

# With custom output directory
python university_email_filter.py "C:\path\to\csv\files" --output "C:\university_results"
```

### Option 3: Interactive Mode
```bash
python university_email_filter.py --interactive
```
Then follow the prompts!

## 📋 What You Need

### Required:
- **Python** installed on your system
- **CSV files** with email data
- **<PERSON>ail column** in your CSV files

### Your CSV files should look like:
```csv
Name,<PERSON><PERSON>,<PERSON>
<PERSON>,<EMAIL>,Private Company
Prof. Wilson,<EMAIL>,MIT
Dr. Chen,<EMAIL>,Oxford University
```

## 📁 What Happens

### Input:
- Your CSV files in a directory
- Example: `data.csv`, `contacts.csv`, `emails.csv`

### Output:
- New directory: `university_filtered/` (or your custom directory)
- Filtered files: `university_data.csv`, `university_contacts.csv`, `university_emails.csv`
- **Only university emails** in the output files

## 🎯 University Emails Detected

The filter finds emails from:

✅ **US Universities**: `@mit.edu`, `@harvard.edu`, `@stanford.edu`
✅ **UK Universities**: `@oxford.ac.uk`, `@cambridge.ac.uk`
✅ **Australian Universities**: `@unimelb.edu.au`, `@unsw.edu.au`
✅ **German Universities**: `@uni-berlin.de`, `@uni-hamburg.de`
✅ **Italian Universities**: `@unibo.it`, `@uniroma1.it`
✅ **Brazilian Universities**: `@unicamp.br`, `@usp.br`
✅ **Generic University Domains**: `@university.edu`, `@college.edu`
✅ **Research Institutes**: `@research.institute.org`

## 🧪 Test First (Recommended)

Before processing your real data, test with examples:

```bash
# See how the filtering works
python university_filter_example.py

# Test university pattern detection
python test_uni_patterns.py
```

## 📊 Example Results

```
=== Processing Complete! ===
Files processed: 3
Total records processed: 15,847
University emails found: 2,341
University email percentage: 14.77%
Output saved to: C:\university_filtered\
```

## 🔧 Troubleshooting

### "Python not found"
- Install Python from python.org
- Make sure Python is in your system PATH

### "No CSV files found"
- Check your directory path
- Make sure files have `.csv` extension

### "Email column not found"
- Check your column name (case-sensitive)
- Use `--email-column "YourColumnName"` parameter

### "Permission denied"
- Make sure you have write permissions to the output directory
- Try running as administrator (Windows)

## 💡 Pro Tips

1. **Backup your data** before processing
2. **Test with a small sample** first
3. **Check the statistics** to verify results make sense
4. **Use custom output directory** to organize results
5. **Process one directory at a time** for large datasets

## 🎯 Integration with Your Workflow

This filter works great with your existing scripts:
- Use it before running `hic.py` or `hic_education.py`
- Integrate with your CSV processing pipeline
- Combine with your country filtering scripts

## 📞 Need Help?

If you encounter issues:
1. Run the test scripts first
2. Check the error messages
3. Verify your CSV file format
4. Make sure Python and pandas are installed

Happy filtering! 🎓📧
